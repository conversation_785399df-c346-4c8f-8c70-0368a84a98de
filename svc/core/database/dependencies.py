"""FastAPI 依赖注入模块"""
import logging
from typing import AsyncGenerator

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from .session import get_session

logger = logging.getLogger(__name__)

async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI 数据库会话依赖"""
    async with get_session() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"API请求会话操作失败: {e}")
            raise

# 便捷的依赖函数
def get_db():
    """获取数据库会话依赖"""
    return Depends(get_db_session)