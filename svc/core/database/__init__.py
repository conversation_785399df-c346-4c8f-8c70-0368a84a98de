"""
数据库模块。
提供数据库连接、模型基类和会话管理功能。
"""

# 基础数据库组件导入


# 会话管理功能导入
from svc.core.database.session import (Base, check_connection_leaks, close_db,
                                       ensure_connection_health,
                                       get_connection_stats, get_db,
                                       get_session, get_session_tracking_stats,
                                       init_engine, recover_connection_pool,
                                       setup_db_pool_monitor)
# 事务管理功能导入
from svc.core.database.transactions import (TransactionTimeoutError,
                                            check_long_running_transactions,
                                            get_active_transactions,
                                            get_transaction_stats,
                                            with_transaction,
                                            with_transaction_timeout)
# 会话工具功能导入
from svc.core.database.utils import (get_session_for_event,
                                     get_session_for_route,
                                     get_session_for_script, log_db_operation,
                                     resolve_dependencies_for_event,
                                     transactional_route)

__all__ = [
    # 基础组件
    "Base",
    
    # 会话管理
    "init_engine",
    "close_db",
    "get_db",
    "get_session",
    "get_connection_stats",
    "ensure_connection_health",
    "get_session_tracking_stats",
    "check_connection_leaks",
    "recover_connection_pool",
    "setup_db_pool_monitor",
    
    # 事务管理
    "with_transaction",
    "with_transaction_timeout",
    "get_transaction_stats",
    "get_active_transactions",
    "check_long_running_transactions",
    "TransactionTimeoutError",
    
    # 会话工具 - 标准化会话获取方式
    "get_session_for_route",
    "get_session_for_script",
    "get_session_for_event",
    "resolve_dependencies_for_event",
    "transactional_route",
    "log_db_operation",

]
