"""数据库引擎管理模块"""
import logging
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from sqlalchemy.pool import AsyncAdaptedQueuePool

from svc.core.config.settings import get_settings

logger = logging.getLogger(__name__)

class DatabaseEngine:
    """数据库引擎管理器"""
    
    def __init__(self):
        self._engine: Optional[AsyncEngine] = None
        self._initialized = False
    
    async def initialize(self, testing: bool = False) -> AsyncEngine:
        """初始化数据库引擎"""
        if self._initialized and self._engine:
            return self._engine
        
        settings = get_settings()
        db_uri = settings.db_test_uri if testing else settings.db_uri
        
        self._engine = create_async_engine(
            db_uri,
            echo=settings.db_echo,
            future=True,
            pool_pre_ping=True,
            poolclass=AsyncAdaptedQueuePool,
            pool_size=settings.db_pool_size,
            max_overflow=settings.db_max_overflow,
            pool_timeout=settings.db_pool_timeout,
            pool_recycle=settings.db_pool_recycle
        )
        
        self._initialized = True
        logger.info(f"数据库引擎已初始化，连接池大小: {settings.db_pool_size}")
        return self._engine
    
    @property
    def engine(self) -> Optional[AsyncEngine]:
        """获取引擎实例"""
        return self._engine
    
    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._initialized
    
    async def close(self) -> None:
        """关闭数据库引擎"""
        if self._engine:
            await self._engine.dispose()
            self._engine = None
            self._initialized = False
            logger.info("数据库引擎已关闭")
    
    async def check_health(self) -> bool:
        """检查数据库连接健康状态"""
        if not self._engine:
            return False
        
        try:
            from sqlalchemy.future import select
            async with self._engine.connect() as conn:
                await conn.execute(select(1))
            return True
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False

# 全局引擎实例
_db_engine = DatabaseEngine()

async def get_engine(testing: bool = False) -> AsyncEngine:
    """获取数据库引擎"""
    if not _db_engine.is_initialized:
        await _db_engine.initialize(testing)
    return _db_engine.engine

async def close_engine() -> None:
    """关闭数据库引擎"""
    await _db_engine.close()

async def check_database_health() -> bool:
    """检查数据库健康状态"""
    return await _db_engine.check_health()