
"""数据库监控模块"""
import logging
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict

from .engine import get_engine

logger = logging.getLogger(__name__)

@dataclass
class DatabaseStats:
    """数据库统计信息"""
    pool_size: int = 0
    pool_checkedout: int = 0
    pool_overflow: int = 0
    pool_checkedin: int = 0
    created_at: datetime = field(default_factory=datetime.now)

class DatabaseMonitor:
    """数据库监控器"""
    
    async def get_pool_stats(self) -> DatabaseStats:
        """获取连接池统计信息"""
        engine = await get_engine()
        if not engine:
            return DatabaseStats()
        
        pool = engine.pool
        return DatabaseStats(
            pool_size=pool.size(),
            pool_checkedout=pool.checkedout(),
            pool_overflow=pool.overflow(),
            pool_checkedin=pool.checkedin()
        )
    
    async def get_health_status(self) -> Dict[str, Any]:
        """获取数据库健康状态"""
        from .engine import check_database_health
        
        stats = await self.get_pool_stats()
        is_healthy = await check_database_health()
        
        usage_ratio = (
            stats.pool_checkedout / stats.pool_size
            if stats.pool_size > 0 else 0
        )
        
        return {
            "healthy": is_healthy,
            "pool_usage_ratio": usage_ratio,
            "pool_stats": {
                "size": stats.pool_size,
                "checkedout": stats.pool_checkedout,
                "overflow": stats.pool_overflow,
                "checkedin": stats.pool_checkedin
            },
            "timestamp": datetime.now().isoformat()
        }

# 全局监控实例
_db_monitor = DatabaseMonitor()

async def get_database_stats() -> DatabaseStats:
    """获取数据库统计信息"""
    return await _db_monitor.get_pool_stats()

async def get_database_health() -> Dict[str, Any]:
    """获取数据库健康状态"""
    return await _db_monitor.get_health_status()