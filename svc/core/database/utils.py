"""
会话工具模块。
提供会话获取和事务处理的标准化函数和装饰器。
解决会话获取方式多样问题，统一规范会话使用模式。
"""

import contextlib
import functools
import logging
from typing import (Annotated, Any, Awaitable, Callable, Dict, Optional,
                    TypeVar, Union, cast)

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from svc.core.database.session import get_db, get_session
from svc.core.database.transactions import \
    with_transaction as _with_transaction
from svc.core.database.transactions import \
    with_transaction_timeout as _with_transaction_timeout

# 类型变量定义
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])

# 配置日志
logger = logging.getLogger(__name__)

# ===== 会话获取方式标准化 =====

def get_session_for_route() -> Callable[[], AsyncSession]:
    """
    获取用于API路由的数据库会话依赖函数
    
    推荐在API路由处理函数中使用此依赖:
    
    Example:
        ```python
        @router.get("/users/{user_id}")
        async def get_user(user_id: int, db: AsyncSession = Depends(get_session_for_route())):
            # 使用db会话
            user = await user_service.get_user(db, user_id)
            return user
        ```
    
    Returns:
        Callable: 依赖函数
    """
    return get_db


async def get_session_for_script() -> AsyncSession:
    """
    获取用于脚本和后台任务的数据库会话

    此函数返回一个已经创建好的会话对象，需要手动管理其生命周期。

    Example:
        ```python
        async def update_stats():
            session = await get_session_for_script()
            try:
                # 使用session
                await session.execute(query)
                await session.commit()
            except Exception as e:
                await session.rollback()
                raise
            finally:
                await session.close()
        ```

    Returns:
        AsyncSession: 数据库会话
    """
    from svc.core.database.session import (_initialized, _session_factory,
                                           init_engine)

    # 确保引擎已初始化
    if not _initialized or _session_factory is None:
        await init_engine()

    # 再次检查会话工厂
    if _session_factory is None:
        raise RuntimeError("数据库会话工厂初始化失败")

    # 直接创建会话，不使用上下文管理器
    session = _session_factory()

    logger.debug(f"脚本会话已创建. 会话ID: {id(session)}")

    return session


# ===== 事务管理装饰器 =====

def with_transaction(auto_commit: bool = True):
    """
    事务装饰器，自动处理会话创建、提交和回滚
    
    这是一个统一规范的用法推荐，内部调用现有的with_transaction实现。
    
    Example:
        ```python
        # 用于服务层方法
        @with_transaction()
        async def create_user(self, db: AsyncSession, data: dict):
            user = User(**data)
            db.add(user)
            return user
        ```
    
    Args:
        auto_commit: 是否自动提交事务
        
    Returns:
        装饰器函数
    """
    return _with_transaction(auto_commit=auto_commit)


def with_transaction_timeout(timeout_seconds: Optional[int] = None):
    """
    事务超时装饰器，在超时时自动回滚事务
    
    这是一个统一规范的用法推荐，内部调用现有的with_transaction_timeout实现。
    
    Example:
        ```python
        # 用于可能长时间运行的操作
        @with_transaction_timeout(timeout_seconds=30)
        async def process_large_dataset(self, db: AsyncSession, data_ids: list):
            for id in data_ids:
                # 处理每条数据...
            return result
        ```
    
    Args:
        timeout_seconds: 超时秒数，如果为None则使用配置的默认值
        
    Returns:
        装饰器函数
    """
    return _with_transaction_timeout(timeout_seconds=timeout_seconds)


# ===== 用于路由层的事务装饰器 =====

def transactional_route(auto_commit: bool = True, timeout_seconds: Optional[int] = None):
    """
    用于API路由的事务管理装饰器
    
    将路由处理函数包装在事务中，支持超时控制。
    此装饰器需放在路由装饰器(@router.get等)之后使用。
    
    Example:
        ```python
        @router.post("/users/")
        @transactional_route(timeout_seconds=10)
        async def create_user(data: UserCreate, db: AsyncSession = Depends(get_session_for_route())):
            user = User(**data.dict())
            db.add(user)
            return user
        ```
    
    Args:
        auto_commit: 是否自动提交事务
        timeout_seconds: 超时秒数，如果为None则使用配置的默认值
        
    Returns:
        装饰器函数
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 应用事务超时装饰器
            if timeout_seconds is not None:
                func_with_timeout = _with_transaction_timeout(timeout_seconds)(func)
            else:
                func_with_timeout = func
                
            # 处理自动提交
            db = kwargs.get('db')
            try:
                result = await func_with_timeout(*args, **kwargs)
                if auto_commit and db is not None and hasattr(db, 'commit'):
                    await db.commit()
                return result
            except Exception as e:
                if db is not None and hasattr(db, 'rollback'):
                    await db.rollback()
                logger.error(f"路由事务操作失败: {func.__name__}, 错误: {str(e)}")
                raise
        
        return cast(F, wrapper)
    
    return decorator


# ===== 日志增强装饰器 =====

def log_db_operation(operation_name: str):
    """
    数据库操作日志记录装饰器
    
    为操作添加统一格式的日志记录，包括操作名称、参数信息等。
    
    Example:
        ```python
        @log_db_operation("创建用户")
        async def create_user(self, db: AsyncSession, data: dict):
            # 数据库操作...
        ```
    
    Args:
        operation_name: 操作名称
        
    Returns:
        装饰器函数
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 记录操作开始
            func_name = f"{func.__module__}.{func.__name__}"
            safe_kwargs = {k: v for k, v in kwargs.items() if k != 'db' and k != 'password'}
            logger.info(f"数据库操作开始: {operation_name} [{func_name}], 参数: {safe_kwargs}")
            
            start_time = __import__('time').time()
            try:
                result = await func(*args, **kwargs)
                execution_time = __import__('time').time() - start_time
                
                # 记录操作成功
                logger.info(
                    f"数据库操作成功: {operation_name} [{func_name}], "
                    f"耗时: {execution_time:.3f}秒"
                )
                return result
            except Exception as e:
                execution_time = __import__('time').time() - start_time
                
                # 记录操作失败
                logger.error(
                    f"数据库操作失败: {operation_name} [{func_name}], "
                    f"耗时: {execution_time:.3f}秒, 错误: {str(e)}"
                )
                raise
        
        return cast(F, wrapper)
    
    return decorator


# ===== 事件处理器支持 =====

@contextlib.asynccontextmanager
async def get_session_for_event():
    """
    获取用于事件处理器的数据库会话上下文管理器

    这个函数复用了现有的 get_db() 逻辑，但适配了事件处理器的使用场景。
    它直接调用 get_db() 异步生成器并提取会话，提供与路由处理器一致的会话管理。

    Example:
        ```python
        # 在事件处理器中使用
        @local_handler.register(event_name=AUTH_USER_LOGGED_IN)
        async def handle_user_login(event: Event):
            async with get_session_for_event() as session:
                user_repo = UserRepository(db=session)
                user_service = UserService(user_repo=user_repo)
                user = await user_service.get_resource_by_id(user_id)
                # 会话会自动提交和关闭
        ```

    Yields:
        AsyncSession: 数据库会话
    """
    # 直接使用现有的 get_db() 异步生成器
    async with get_db() as session:
        yield session


async def resolve_dependencies_for_event(**dependencies):
    """
    为事件处理器解析 FastAPI 依赖项

    这个函数允许事件处理器复用现有的依赖注入逻辑，
    通过手动解析依赖项来获取服务实例。

    Args:
        **dependencies: 依赖项字典，键为参数名，值为依赖函数

    Example:
        ```python
        # 在事件处理器中使用
        from svc.apps.auth.dependencies import get_user_service, get_auth_service

        @local_handler.register(event_name=AUTH_USER_LOGGED_IN)
        async def handle_user_login(event: Event):
            # 解析依赖项
            deps = await resolve_dependencies_for_event(
                user_service=get_user_service,
                auth_service=get_auth_service
            )

            user_service = deps['user_service']
            auth_service = deps['auth_service']

            # 使用服务
            user = await user_service.get_resource_by_id(user_id)
        ```

    Returns:
        Dict[str, Any]: 解析后的依赖项字典
    """
    resolved = {}

    # 创建一个临时的数据库会话用于依赖解析
    async with get_session_for_event() as session:
        for name, dependency_func in dependencies.items():
            try:
                # 检查依赖函数是否需要数据库会话
                import inspect
                sig = inspect.signature(dependency_func)

                # 如果依赖函数需要数据库会话参数，传入会话
                if any(param.annotation == AsyncSession or
                       (hasattr(param.annotation, '__origin__') and
                        param.annotation.__origin__ is Annotated and
                        AsyncSession in param.annotation.__args__)
                       for param in sig.parameters.values()):
                    resolved[name] = await dependency_func(session)
                else:
                    resolved[name] = await dependency_func()

            except Exception as e:
                logger.error(f"解析依赖项 {name} 失败: {e}")
                raise

    return resolved